{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "files.associations": {"*.css": "tailwindcss"}, "tailwindCSS.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "search.exclude": {"**/node_modules": true, "**/dist": true, "**/coverage": true, "**/.git": true}, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/coverage": true}, "eslint.workingDirectories": ["."], "eslint.options": {"configFile": "./config/eslint.config.js"}, "[dockerfile]": {"editor.defaultFormatter": "ms-azuretools.vscode-containers"}}